package responses

import (
	"context"
	"encoding/json"
	"log/slog"
	"net/http"

	"github.com/go-chi/chi/v5/middleware"
	"github.com/information-sharing-networks/signalsd/app/internal/apperrors"
)

type ErrorResponse struct {
	StatusCode int                 `json:"-"`
	ErrorCode  apperrors.ErrorCode `json:"error_code" example:"example_error_code"`
	Message    string              `json:"message" example:"message describing the error"`
	ReqID      string              `json:"-"`
}

// ErrorDetails holds error information for context enrichment
type ErrorDetails struct {
	ErrorCode apperrors.ErrorCode
	Message   string
}

// contextKey is used for storing error details in context
type contextKey string

const errorDetailsKey contextKey = "error_details"

func RespondWithError(w http.ResponseWriter, r *http.Request, statusCode int, errorCode apperrors.ErrorCode, message string) {
	requestID := middleware.GetReqID(r.Context())

	// Store error details in context for middleware to log
	errorDetails := ErrorDetails{
		ErrorCode: errorCode,
		Message:   message,
	}

	// Add error details to context - middleware will pick this up for logging
	ctx := context.WithValue(r.Context(), errorDetailsKey, errorDetails)
	*r = *r.WithContext(ctx)

	errResponse := ErrorResponse{
		StatusCode: statusCode,
		ErrorCode:  errorCode,
		Message:    message,
		ReqID:      requestID,
	}

	dat, err := json.Marshal(errResponse)
	if err != nil {
		// Log marshal error directly since this is a critical system error
		slog.Error("error marshaling error response", slog.String("error", err.Error()), slog.String("request_id", requestID))
		w.WriteHeader(http.StatusInternalServerError)
		_, _ = w.Write([]byte(`{"error_code":"internal_error","message":"Internal Server Error"}`))
		return
	}

	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	w.WriteHeader(statusCode)
	_, _ = w.Write(dat)
}

func RespondWithJSON(w http.ResponseWriter, status int, payload any) {
	if status == http.StatusNoContent {
		w.WriteHeader(status)
		return
	}

	data, err := json.Marshal(payload)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		_, _ = w.Write([]byte(`{"error_code":"marshal_error","message":"Internal Server Error"}`))
		return
	}

	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	w.WriteHeader(status)
	_, _ = w.Write(data)
}

func RespondWithStatusCodeOnly(w http.ResponseWriter, status int) {
	w.WriteHeader(status)
}

// GetErrorDetailsFromContext extracts error details from request context
// This is used by logging middleware to include error information in logs
func GetErrorDetailsFromContext(ctx context.Context) (ErrorDetails, bool) {
	details, ok := ctx.Value(errorDetailsKey).(ErrorDetails)
	return details, ok
}
