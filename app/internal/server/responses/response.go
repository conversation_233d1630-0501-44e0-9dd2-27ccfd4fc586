package responses

import (
	"context"
	"encoding/json"
	"log/slog"
	"net/http"

	"github.com/go-chi/chi/v5/middleware"
	"github.com/information-sharing-networks/signalsd/app/internal/apperrors"
)

type ErrorResponse struct {
	StatusCode int                 `json:"-"`
	ErrorCode  apperrors.ErrorCode `json:"error_code" example:"example_error_code"`
	Message    string              `json:"message" example:"message describing the error"`
	ReqID      string              `json:"-"`
}

// contextKey for storing log attributes
type contextKey string

const logAttrsKey contextKey = "log_attrs"

// AddLogAttrs adds slog attributes to the request context for the logging middleware to include
func AddLogAttrs(ctx context.Context, attrs ...slog.Attr) context.Context {
	existing, _ := ctx.Value(logAttrsKey).([]slog.Attr)
	combined := append(existing, attrs...)
	return context.WithValue(ctx, logAttrsKey, combined)
}

// GetLogAttrs retrieves log attributes from context (used by logging middleware)
func GetLogAttrs(ctx context.Context) []slog.Attr {
	attrs, _ := ctx.Value(logAttrsKey).([]slog.Attr)
	return attrs
}

func RespondWithError(w http.ResponseWriter, r *http.Request, statusCode int, errorCode apperrors.ErrorCode, message string) {
	requestID := middleware.GetReqID(r.Context())

	// Add error details to context for logging middleware to include
	ctx := AddLogAttrs(r.Context(),
		slog.String("error_code", string(errorCode)),
		slog.String("error_message", message),
	)
	*r = *r.WithContext(ctx)

	errResponse := ErrorResponse{
		StatusCode: statusCode,
		ErrorCode:  errorCode,
		Message:    message,
		ReqID:      requestID,
	}

	dat, err := json.Marshal(errResponse)
	if err != nil {
		// Log marshal error directly since this is a critical system error
		slog.Error("error marshaling error response", slog.String("error", err.Error()), slog.String("request_id", requestID))
		w.WriteHeader(http.StatusInternalServerError)
		_, _ = w.Write([]byte(`{"error_code":"internal_error","message":"Internal Server Error"}`))
		return
	}

	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	w.WriteHeader(statusCode)
	_, _ = w.Write(dat)
}

func RespondWithJSON(w http.ResponseWriter, status int, payload any) {
	if status == http.StatusNoContent {
		w.WriteHeader(status)
		return
	}

	data, err := json.Marshal(payload)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		_, _ = w.Write([]byte(`{"error_code":"marshal_error","message":"Internal Server Error"}`))
		return
	}

	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	w.WriteHeader(status)
	_, _ = w.Write(data)
}

func RespondWithStatusCodeOnly(w http.ResponseWriter, status int) {
	w.WriteHeader(status)
}
