package responses

import (
	"context"
	"encoding/json"
	"log/slog"
	"net/http"

	"github.com/go-chi/chi/v5/middleware"
	"github.com/information-sharing-networks/signalsd/app/internal/apperrors"
)

type ErrorResponse struct {
	StatusCode int                 `json:"-"`
	ErrorCode  apperrors.ErrorCode `json:"error_code" example:"example_error_code"`
	Message    string              `json:"message" example:"message describing the error"`
	ReqID      string              `json:"-"`
}

// contextKey is used for storing error information in context
type contextKey string

const errorContextKey contextKey = "error"

// SetError stores error information in the request context for logging middleware
func SetError(ctx context.Context, errorCode apperrors.ErrorCode, message string) context.Context {
	errorInfo := map[string]string{
		"error_code": string(errorCode),
		"message":    message,
	}
	return context.WithValue(ctx, errorContextKey, errorInfo)
}

// GetError retrieves error information from context (used by logging middleware)
func GetError(ctx context.Context) (map[string]string, bool) {
	errorInfo, ok := ctx.Value(errorContextKey).(map[string]string)
	return errorInfo, ok
}

func RespondWithError(w http.ResponseWriter, r *http.Request, statusCode int, errorCode apperrors.ErrorCode, message string) {
	requestID := middleware.GetReqID(r.Context())

	// Store error details in context for logging middleware (httplog pattern)
	ctx := SetError(r.Context(), errorCode, message)
	*r = *r.WithContext(ctx)

	errResponse := ErrorResponse{
		StatusCode: statusCode,
		ErrorCode:  errorCode,
		Message:    message,
		ReqID:      requestID,
	}

	dat, err := json.Marshal(errResponse)
	if err != nil {
		// Log marshal error directly since this is a critical system error
		slog.Error("error marshaling error response", slog.String("error", err.Error()), slog.String("request_id", requestID))
		w.WriteHeader(http.StatusInternalServerError)
		_, _ = w.Write([]byte(`{"error_code":"internal_error","message":"Internal Server Error"}`))
		return
	}

	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	w.WriteHeader(statusCode)
	_, _ = w.Write(dat)
}

func RespondWithJSON(w http.ResponseWriter, status int, payload any) {
	if status == http.StatusNoContent {
		w.WriteHeader(status)
		return
	}

	data, err := json.Marshal(payload)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		_, _ = w.Write([]byte(`{"error_code":"marshal_error","message":"Internal Server Error"}`))
		return
	}

	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	w.WriteHeader(status)
	_, _ = w.Write(data)
}

func RespondWithStatusCodeOnly(w http.ResponseWriter, status int) {
	w.WriteHeader(status)
}
